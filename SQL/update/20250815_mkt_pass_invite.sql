
SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mkt_passenger_invite_campaign
-- ----------------------------
DROP TABLE IF EXISTS `mkt_passenger_invite_campaign`;
CREATE TABLE `mkt_passenger_invite_campaign`
(
    `id`            bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `activity_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '活动名称',
    `start_time`    datetime                                                      NOT NULL COMMENT '活动开始时间',
    `end_time`      datetime                                                      NOT NULL COMMENT '活动结束时间',
    `status`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci      NOT NULL DEFAULT '0' COMMENT '活动状态 0:待开始 1:进行中 2:已结束',
    `rule_content`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '活动规则',
    `max_invites`   int NULL DEFAULT 0 COMMENT '单个用户最多可邀请人数(0表示不限)',
    `share_title`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分享标题',
    `entrance_text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '首页文案',
    `city_code`     json                                                          NOT NULL COMMENT '城市code数组',
    `create_dept`   bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`     bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`   datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`     bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`   datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX           `idx_time_range`(`start_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '乘客推乘客活动主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mkt_passenger_invite_code
-- ----------------------------
DROP TABLE IF EXISTS `mkt_passenger_invite_code`;
CREATE TABLE `mkt_passenger_invite_code`
(
    `id`          bigint                                                       NOT NULL AUTO_INCREMENT,
    `code`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邀请码',
    `inviter_id`  bigint                                                       NOT NULL COMMENT '邀请人ID',
    `activity_id` bigint                                                       NOT NULL COMMENT '活动ID',
    `qrcode_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分享码url',
    `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`   bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`   bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '乘客推乘客邀请码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mkt_passenger_invite_record
-- ----------------------------
DROP TABLE IF EXISTS `mkt_passenger_invite_record`;
CREATE TABLE `mkt_passenger_invite_record`
(
    `id`                  bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `campaign_id`         bigint                                                       NOT NULL COMMENT '活动ID',
    `inviter_id`          bigint                                                       NOT NULL COMMENT '邀请人乘客ID',
    `inviter_mobile`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邀请人手机号',
    `inviter_city_code`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邀请人城市',
    `invitee_id`          bigint                                                       NOT NULL COMMENT '被邀请人乘客ID',
    `invitee_mobile`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '被邀请人手机号',
    `invitee_city_code`   varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '被邀请人城市',
    `invite_time`         datetime                                                     NOT NULL COMMENT '邀请时间',
    `order_complete_time` datetime NULL DEFAULT NULL COMMENT '首单完成时间',
    `order_id`            bigint NULL DEFAULT NULL COMMENT '首单id',
    `status`              char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     NOT NULL DEFAULT '0' COMMENT '邀请状态(1已注册,2已首单)',
    `create_dept`         bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`           bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`         datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`           bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`         datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uniq_invite`(`campaign_id` ASC, `invitee_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '乘客推乘客邀请记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mkt_passenger_invite_reward
-- ----------------------------
DROP TABLE IF EXISTS `mkt_passenger_invite_reward`;
CREATE TABLE `mkt_passenger_invite_reward`
(
    `id`               bigint                                                   NOT NULL AUTO_INCREMENT COMMENT '奖励记录ID',
    `campaign_id`      bigint                                                   NOT NULL COMMENT '活动ID',
    `invite_record_id` bigint                                                   NOT NULL COMMENT '对应邀请记录ID',
    `reward_config_id` bigint                                                   NOT NULL COMMENT '奖励配置ID',
    `passenger_id`     bigint                                                   NOT NULL COMMENT '奖励接收人乘客ID',
    `role_type`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色类型(1邀请人,2被邀请人)',
    `condition_type`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '发放条件(1注册成功,2完成首单)',
    `reward_type`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '1=现金 2=积分 3=卡券',
    `reward_value`     decimal(10, 2) NULL DEFAULT NULL COMMENT '奖励金额或积分',
    `reward_meta`      json NULL COMMENT '扩展字段(如发放的具体券ID数组)',
    `coupon_grant_id`  bigint NULL DEFAULT NULL COMMENT '优惠券发放表id',
    `status`           char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)',
    `create_dept`      bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`        bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`      datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`        bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`      datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX              `idx_passenger`(`passenger_id` ASC) USING BTREE,
    INDEX              `idx_invite_record`(`invite_record_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '乘客推乘客奖励发放记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mkt_passenger_invite_reward_config
-- ----------------------------
DROP TABLE IF EXISTS `mkt_passenger_invite_reward_config`;
CREATE TABLE `mkt_passenger_invite_reward_config`
(
    `id`             bigint                                                   NOT NULL AUTO_INCREMENT COMMENT '奖励配置ID',
    `campaign_id`    bigint                                                   NOT NULL COMMENT '活动ID',
    `role_type`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色类型(1邀请人,2被邀请人)',
    `reward_type`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '奖励类型：1=现金 2=优惠券 3=积分',
    `reward_value`   decimal(10, 2) NULL DEFAULT NULL COMMENT '奖励金额/积分值(现金或积分用)',
    `reward_meta`    json NULL COMMENT '扩展字段(如coupon_ids:[\"1001\",\"1002\"])',
    `condition_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '发放条件(1注册成功,2完成首单)',
    `create_dept`    bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`      bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`    datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`    datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `idx_campaign_role`(`campaign_id` ASC, `role_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '乘客推乘客活动奖励配置表' ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;
