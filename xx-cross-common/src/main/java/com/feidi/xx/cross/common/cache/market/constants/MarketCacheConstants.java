package com.feidi.xx.cross.common.cache.market.constants;

import com.feidi.xx.common.core.constant.GlobalConstants;

import static com.feidi.xx.cross.common.constant.market.MarketCacheConstants.CACHE_PREFIX;


/**
 * 订单服务 缓存常量
 *
 * <AUTHOR>
 */
public interface MarketCacheConstants {

    /**
     * 营销服务相关缓存前缀
     */
    String CACHE_PREFIX = "mkt:";

    /**
     * 代客下单次数枚举
     */
    String AGENT_ORDER_LIMIT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order-limit:";

    /**
     * 代客下单绑定乘客关系
     */
    String AGENT_ORDER_BIND_PASSENGER_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "order-bind-passenger:";

    /**
     * 邀请关系配置
     */
    String INVITE_CONFIG_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "invite-config:";

    /**
     * 优惠券库存
     */
    String COUPON_STOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "coupon-stock:";


    /**
     * 优惠券用户领取记录
     */

    String COUPON_USER_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX +"user:";

    /**
     * 优惠券用户领取限制
     */
    String COUPON_USER_COUNT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX +"user-count:";

    String COUPON_STOCK_LOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"coupon-stock-lock:";

    /**
     * 定向放券，文件上传缓存
     */
    String TARGETED_COUPONS_KEY = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"targeted-coupons:";

    /**
     * 邀请码 -> 用户ID
     */
    String INVITE_CODE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"invite:code:";
    /**
     * 用户ID -> 邀请码
     */
    String USER_INVITE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"invite:user:";
    /**
     * 活动下所有邀请码集合
     */
    String CODE_SET_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX+"invite:codes:";

}
