package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1=现金 2=积分 3=卡券
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerInviteRewardTypeEnum {

    CASH("1", "现金"),
    INTEGRAL("2", "积分"),
    COUPON("3", "卡券");

    private final String code;

    private final String info;

    /**
     * 获取枚举值
     *
     * @param code 枚举值
     * @return 枚举值
     */
    public static PassengerInviteRewardTypeEnum getByCode(String code) {
        for (PassengerInviteRewardTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
