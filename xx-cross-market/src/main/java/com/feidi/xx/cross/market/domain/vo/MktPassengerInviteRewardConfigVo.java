package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRewardConfig;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 乘客推乘客活动奖励配置视图对象 mkt_passenger_invite_reward_config
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktPassengerInviteRewardConfig.class)
public class MktPassengerInviteRewardConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 奖励配置ID
     */
    @ExcelProperty(value = "奖励配置ID")
    private Long id;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long campaignId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     */
    @ExcelProperty(value = "角色类型(1邀请人,2被邀请人)")
    private String roleType;

    /**
     * 奖励类型：1=现金 2=优惠券 3=积分
     */
    @ExcelProperty(value = "奖励类型：1=现金 2=优惠券 3=积分")
    private String rewardType;

    /**
     * 奖励金额/积分值(现金或积分用)
     */
    @ExcelProperty(value = "奖励金额/积分值(现金或积分用)")
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如coupon_ids:["1001","1002"])
     */
    @ExcelProperty(value = "1")
    private RewardMeta rewardMeta;

    /**
     * 发放条件(1注册成功,2完成首单)
     */
    @ExcelProperty(value = "发放条件(1注册成功,2完成首单)")
    private String conditionType;


}
