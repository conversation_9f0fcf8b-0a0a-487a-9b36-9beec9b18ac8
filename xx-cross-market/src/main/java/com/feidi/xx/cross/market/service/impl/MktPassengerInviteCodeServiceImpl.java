package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCode;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCodeBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCodeVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCodeMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客邀请码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteCodeServiceImpl implements IMktPassengerInviteCodeService {

    private final MktPassengerInviteCodeMapper baseMapper;

    /**
     * 查询乘客推乘客邀请码
     *
     * @param id 主键
     * @return 乘客推乘客邀请码
     */
    @Override
    public MktPassengerInviteCodeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客邀请码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客邀请码分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteCodeVo> queryPageList(MktPassengerInviteCodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteCode> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteCodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客邀请码列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客邀请码列表
     */
    @Override
    public List<MktPassengerInviteCodeVo> queryList(MktPassengerInviteCodeBo bo) {
        LambdaQueryWrapper<MktPassengerInviteCode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteCode> buildQueryWrapper(MktPassengerInviteCodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteCode> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), MktPassengerInviteCode::getCode, bo.getCode());
        lqw.eq(bo.getInviterId() != null, MktPassengerInviteCode::getInviterId, bo.getInviterId());
        lqw.eq(bo.getActivityId() != null, MktPassengerInviteCode::getActivityId, bo.getActivityId());
        lqw.eq(StringUtils.isNotBlank(bo.getQrcodeUrl()), MktPassengerInviteCode::getQrcodeUrl, bo.getQrcodeUrl());
        return lqw;
    }

    /**
     * 新增乘客推乘客邀请码
     *
     * @param bo 乘客推乘客邀请码
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteCodeBo bo) {
        MktPassengerInviteCode add = MapstructUtils.convert(bo, MktPassengerInviteCode.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客邀请码
     *
     * @param bo 乘客推乘客邀请码
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteCodeBo bo) {
        MktPassengerInviteCode update = MapstructUtils.convert(bo, MktPassengerInviteCode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteCode entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客推乘客邀请码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
