package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCampaign;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCampaignMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客活动主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteCampaignServiceImpl implements IMktPassengerInviteCampaignService {

    private final MktPassengerInviteCampaignMapper baseMapper;

    /**
     * 查询乘客推乘客活动主
     *
     * @param id 主键
     * @return 乘客推乘客活动主
     */
    @Override
    public MktPassengerInviteCampaignVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动主分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteCampaignVo> queryPageList(MktPassengerInviteCampaignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteCampaignVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客活动主列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动主列表
     */
    @Override
    public List<MktPassengerInviteCampaignVo> queryList(MktPassengerInviteCampaignBo bo) {
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteCampaign> buildQueryWrapper(MktPassengerInviteCampaignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getActivityName()), MktPassengerInviteCampaign::getActivityName, bo.getActivityName());
        lqw.eq(bo.getStartTime() != null, MktPassengerInviteCampaign::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, MktPassengerInviteCampaign::getEndTime, bo.getEndTime());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteCampaign::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleContent()), MktPassengerInviteCampaign::getRuleContent, bo.getRuleContent());
        lqw.eq(bo.getMaxInvites() != null, MktPassengerInviteCampaign::getMaxInvites, bo.getMaxInvites());
        lqw.eq(StringUtils.isNotBlank(bo.getShareTitle()), MktPassengerInviteCampaign::getShareTitle, bo.getShareTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getEntranceText()), MktPassengerInviteCampaign::getEntranceText, bo.getEntranceText());
        lqw.eq(CollUtil.isNotEmpty(bo.getCityCode()), MktPassengerInviteCampaign::getCityCode, bo.getCityCode());
        return lqw;
    }

    /**
     * 新增乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteCampaignBo bo) {
        MktPassengerInviteCampaign add = MapstructUtils.convert(bo, MktPassengerInviteCampaign.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteCampaignBo bo) {
        MktPassengerInviteCampaign update = MapstructUtils.convert(bo, MktPassengerInviteCampaign.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteCampaign entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客推乘客活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
