package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCampaign;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 乘客推乘客活动主业务对象 mkt_passenger_invite_campaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktPassengerInviteCampaign.class, reverseConvertGenerate = false)
public class MktPassengerInviteCampaignBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String activityName;

    /**
     * 活动开始时间
     */
    @NotNull(message = "活动开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startTime;

    /**
     * 活动结束时间
     */
    @NotNull(message = "活动结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     */
    @NotNull(message = "活动状态 0:待开始 1:进行中 2:已结束不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 活动规则
     */
    @NotBlank(message = "活动规则不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ruleContent;

    /**
     * 单个用户最多可邀请人数(0表示不限)
     */
    @NotNull(message = "单个用户最多可邀请人数(0表示不限)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long maxInvites;

    /**
     * 分享标题
     */
    @NotBlank(message = "分享标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String shareTitle;

    /**
     * 首页文案
     */
    @NotBlank(message = "首页文案不能为空", groups = {AddGroup.class, EditGroup.class})
    private String entranceText;

    /**
     * 城市code数组
     */
    @NotBlank(message = "城市code数组不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<String> cityCode;


}
