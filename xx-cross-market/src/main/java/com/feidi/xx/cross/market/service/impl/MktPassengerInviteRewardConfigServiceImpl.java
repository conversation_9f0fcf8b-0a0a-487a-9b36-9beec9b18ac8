package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRewardConfig;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardConfigMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客活动奖励配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRewardConfigServiceImpl implements IMktPassengerInviteRewardConfigService {

    private final MktPassengerInviteRewardConfigMapper baseMapper;

    /**
     * 查询乘客推乘客活动奖励配置
     *
     * @param id 主键
     * @return 乘客推乘客活动奖励配置
     */
    @Override
    public MktPassengerInviteRewardConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客活动奖励配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动奖励配置分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRewardConfigVo> queryPageList(MktPassengerInviteRewardConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRewardConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客活动奖励配置列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动奖励配置列表
     */
    @Override
    public List<MktPassengerInviteRewardConfigVo> queryList(MktPassengerInviteRewardConfigBo bo) {
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteRewardConfig> buildQueryWrapper(MktPassengerInviteRewardConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteRewardConfig::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getRoleType() != null, MktPassengerInviteRewardConfig::getRoleType, bo.getRoleType());
        lqw.eq(bo.getRewardType() != null, MktPassengerInviteRewardConfig::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardValue() != null, MktPassengerInviteRewardConfig::getRewardValue, bo.getRewardValue());
        lqw.eq(bo.getConditionType() != null, MktPassengerInviteRewardConfig::getConditionType, bo.getConditionType());
        return lqw;
    }

    /**
     * 新增乘客推乘客活动奖励配置
     *
     * @param bo 乘客推乘客活动奖励配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRewardConfigBo bo) {
        MktPassengerInviteRewardConfig add = MapstructUtils.convert(bo, MktPassengerInviteRewardConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客活动奖励配置
     *
     * @param bo 乘客推乘客活动奖励配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRewardConfigBo bo) {
        MktPassengerInviteRewardConfig update = MapstructUtils.convert(bo, MktPassengerInviteRewardConfig.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteRewardConfig entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客推乘客活动奖励配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
