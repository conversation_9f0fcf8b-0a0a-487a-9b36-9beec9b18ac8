package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 乘客推乘客活动奖励配置对象 mkt_passenger_invite_reward_config
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mkt_passenger_invite_reward_config", autoResultMap = true)
public class MktPassengerInviteRewardConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 奖励配置ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动ID
     */
    private Long campaignId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum
     */
    private String roleType;

    /**
     * 奖励类型：1=现金 2=优惠券 3=积分
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum
     */
    private String rewardType;

    /**
     * 奖励金额/积分值(现金或积分用)
     */
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如coupon_ids:["1001","1002"])
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private RewardMeta rewardMeta;

    /**
     * 发放条件(1注册成功,2完成首单)
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum
     */
    private String conditionType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
