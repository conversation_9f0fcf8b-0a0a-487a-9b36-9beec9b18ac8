package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteReward;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客奖励发放记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRewardServiceImpl implements IMktPassengerInviteRewardService {

    private final MktPassengerInviteRewardMapper baseMapper;

    /**
     * 查询乘客推乘客奖励发放记录
     *
     * @param id 主键
     * @return 乘客推乘客奖励发放记录
     */
    @Override
    public MktPassengerInviteRewardVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客奖励发放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客奖励发放记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRewardVo> queryPageList(MktPassengerInviteRewardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRewardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客奖励发放记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客奖励发放记录列表
     */
    @Override
    public List<MktPassengerInviteRewardVo> queryList(MktPassengerInviteRewardBo bo) {
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteReward> buildQueryWrapper(MktPassengerInviteRewardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteReward> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteReward::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviteRecordId() != null, MktPassengerInviteReward::getInviteRecordId, bo.getInviteRecordId());
        lqw.eq(bo.getRewardConfigId() != null, MktPassengerInviteReward::getRewardConfigId, bo.getRewardConfigId());
        lqw.eq(bo.getPassengerId() != null, MktPassengerInviteReward::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getRoleType() != null, MktPassengerInviteReward::getRoleType, bo.getRoleType());
        lqw.eq(bo.getConditionType() != null, MktPassengerInviteReward::getConditionType, bo.getConditionType());
        lqw.eq(bo.getRewardType() != null, MktPassengerInviteReward::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardValue() != null, MktPassengerInviteReward::getRewardValue, bo.getRewardValue());
        lqw.eq(bo.getCouponGrantId() != null, MktPassengerInviteReward::getCouponGrantId, bo.getCouponGrantId());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteReward::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward add = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRewardBo bo) {
        MktPassengerInviteReward update = MapstructUtils.convert(bo, MktPassengerInviteReward.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteReward entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客推乘客奖励发放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
