package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 乘客推乘客邀请记录视图对象 mkt_passenger_invite_record
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktPassengerInviteRecord.class)
public class MktPassengerInviteRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long id;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long campaignId;

    /**
     * 邀请人乘客ID
     */
    @ExcelProperty(value = "邀请人乘客ID")
    private Long inviterId;

    /**
     * 邀请人手机号
     */
    @ExcelProperty(value = "邀请人手机号")
    private String inviterMobile;

    /**
     * 邀请人城市
     */
    @ExcelProperty(value = "邀请人城市")
    private String inviterCityCode;

    /**
     * 被邀请人乘客ID
     */
    @ExcelProperty(value = "被邀请人乘客ID")
    private Long inviteeId;

    /**
     * 被邀请人手机号
     */
    @ExcelProperty(value = "被邀请人手机号")
    private String inviteeMobile;

    /**
     * 被邀请人城市
     */
    @ExcelProperty(value = "被邀请人城市")
    private String inviteeCityCode;

    /**
     * 邀请时间
     */
    @ExcelProperty(value = "邀请时间")
    private Date inviteTime;

    /**
     * 首单完成时间
     */
    @ExcelProperty(value = "首单完成时间")
    private Date orderCompleteTime;

    /**
     * 首单id
     */
    @ExcelProperty(value = "首单id")
    private Long orderId;

    /**
     * 邀请状态(1已注册,2已首单)
     *
     * @see PassengerInviteStatusEnum
     */
    @ExcelProperty(value = "邀请状态(1已注册,2已首单)")
    private String status;


}
