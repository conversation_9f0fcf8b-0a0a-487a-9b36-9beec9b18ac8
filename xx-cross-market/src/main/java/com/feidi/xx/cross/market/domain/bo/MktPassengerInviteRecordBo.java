package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 乘客推乘客邀请记录业务对象 mkt_passenger_invite_record
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktPassengerInviteRecord.class, reverseConvertGenerate = false)
public class MktPassengerInviteRecordBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long campaignId;

    /**
     * 邀请人乘客ID
     */
    @NotNull(message = "邀请人乘客ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inviterId;

    /**
     * 邀请人手机号
     */
    @NotBlank(message = "邀请人手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviterMobile;

    /**
     * 邀请人城市
     */
    @NotBlank(message = "邀请人城市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviterCityCode;

    /**
     * 被邀请人乘客ID
     */
    @NotNull(message = "被邀请人乘客ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inviteeId;

    /**
     * 被邀请人手机号
     */
    @NotBlank(message = "被邀请人手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviteeMobile;

    /**
     * 被邀请人城市
     */
    @NotBlank(message = "被邀请人城市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String inviteeCityCode;

    /**
     * 邀请时间
     */
    @NotNull(message = "邀请时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date inviteTime;

    /**
     * 首单完成时间
     */
    @NotNull(message = "首单完成时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date orderCompleteTime;

    /**
     * 首单id
     */
    @NotNull(message = "首单id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 邀请状态(1已注册,2已首单)
     *
     * @see PassengerInviteStatusEnum
     */
    @NotNull(message = "邀请状态(1已注册,2已首单)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;


}
