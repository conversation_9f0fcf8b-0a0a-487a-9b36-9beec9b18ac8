package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 乘客推乘客活动主
 * 前端访问路由地址为:/market/passengerInviteCampaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passengerInviteCampaign")
public class MktPassengerInviteCampaignController extends BaseController {

    private final IMktPassengerInviteCampaignService mktPassengerInviteCampaignService;

    /**
     * 查询乘客推乘客活动主列表
     */
    @SaCheckPermission("market:passengerInviteCampaign:list")
    @GetMapping("/list")
    public TableDataInfo<MktPassengerInviteCampaignVo> list(MktPassengerInviteCampaignBo bo, PageQuery pageQuery) {
        return mktPassengerInviteCampaignService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取乘客推乘客活动主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:passengerInviteCampaign:query")
    @GetMapping("/{id}")
    public R<MktPassengerInviteCampaignVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long id) {
        return R.ok(mktPassengerInviteCampaignService.queryById(id));
    }

    /**
     * 新增乘客推乘客活动主
     */
    @SaCheckPermission("market:passengerInviteCampaign:add")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        return toAjax(mktPassengerInviteCampaignService.insertByBo(bo));
    }

    /**
     * 修改乘客推乘客活动主
     */
    @SaCheckPermission("market:passengerInviteCampaign:edit")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        return toAjax(mktPassengerInviteCampaignService.updateByBo(bo));
    }

    /**
     * 删除乘客推乘客活动主
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:passengerInviteCampaign:remove")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktPassengerInviteCampaignService.deleteWithValidByIds(List.of(ids), true));
    }
}
